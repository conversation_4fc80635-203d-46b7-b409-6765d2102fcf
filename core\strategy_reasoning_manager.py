"""
Strategy Reasoning Manager

Advanced reasoning system that generates, evaluates, and optimizes building strategies
using multiple reasoning paradigms and cross-domain pattern recognition.
"""

import asyncio
import logging
import time
import random
import math
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class ReasoningType(Enum):
    """Types of reasoning approaches"""
    CAUSAL = "causal"
    ANALOGICAL = "analogical"
    COUNTERFACTUAL = "counterfactual"
    ABDUCTIVE = "abductive"
    DEDUCTIVE = "deductive"
    INDUCTIVE = "inductive"
    SYSTEMS_THINKING = "systems_thinking"
    CONSTRAINT_BASED = "constraint_based"


class StrategyType(Enum):
    """Types of build strategies"""
    PHYSICS_BASED = "physics_based"
    COMPUTATIONAL = "computational"
    HEURISTIC = "heuristic"
    BIO_INSPIRED = "bio_inspired"
    META_ALGORITHMIC = "meta_algorithmic"
    HYBRID = "hybrid"
    EXPERIMENTAL = "experimental"


class FeasibilityLevel(Enum):
    """Feasibility assessment levels"""
    IMPOSSIBLE = "impossible"
    EXTREMELY_DIFFICULT = "extremely_difficult"
    CHALLENGING = "challenging"
    MODERATE = "moderate"
    STRAIGHTFORWARD = "straightforward"


@dataclass
class ReasoningStep:
    """Individual step in a reasoning chain"""
    reasoning_type: ReasoningType
    premise: str
    inference: str
    conclusion: str
    confidence: float
    supporting_evidence: List[str] = field(default_factory=list)
    assumptions: List[str] = field(default_factory=list)


@dataclass
class ReasoningChain:
    """Complete reasoning chain for strategy development"""
    goal: str
    steps: List[ReasoningStep]
    overall_confidence: float
    alternative_paths: List['ReasoningChain'] = field(default_factory=list)
    meta_reasoning: str = ""


@dataclass
class BuildStrategy:
    """Comprehensive build strategy definition"""
    id: str
    name: str
    description: str
    strategy_type: StrategyType
    reasoning_chain: ReasoningChain
    
    # Feasibility Assessment
    feasibility_level: FeasibilityLevel
    resource_requirements: Dict[str, Any]
    expected_difficulty: float
    success_probability: float
    
    # Implementation Details
    approach_steps: List[str]
    tools_required: List[str]
    skills_required: List[str]
    
    # Risk Assessment
    risk_factors: List[str]
    mitigation_strategies: List[str]
    failure_modes: List[str]
    
    # Learning Integration
    similar_successes: List[str] = field(default_factory=list)
    related_failures: List[str] = field(default_factory=list)
    domain_expertise_required: List[str] = field(default_factory=list)
    
    # Execution Parameters
    execution_order: int = 0
    parallel_compatible: bool = True
    dependencies: List[str] = field(default_factory=list)
    alternative_strategies: List[str] = field(default_factory=list)


@dataclass
class OptimizationCriteria:
    """Criteria for strategy optimization"""
    prioritize_speed: bool = False
    prioritize_reliability: bool = True
    prioritize_novelty: bool = False
    prioritize_learning: bool = True
    resource_constraints: Dict[str, float] = field(default_factory=dict)
    risk_tolerance: float = 0.7


@dataclass
class FeasibilityAssessment:
    """Detailed feasibility analysis"""
    overall_feasibility: FeasibilityLevel
    technical_feasibility: float
    resource_feasibility: float
    time_feasibility: float
    knowledge_feasibility: float
    
    critical_assumptions: List[str]
    blocking_factors: List[str]
    success_enablers: List[str]
    confidence_factors: Dict[str, float]


class StrategyReasoningManager:
    """
    Advanced reasoning system for strategy generation and optimization
    """
    
    def __init__(self, agi_system, enhanced_personality=None):
        self.agi_system = agi_system
        self.enhanced_personality = enhanced_personality
        
        # Strategy repositories
        self.strategy_templates = self._initialize_strategy_templates()
        self.reasoning_patterns = self._initialize_reasoning_patterns()
        self.domain_knowledge = self._initialize_domain_knowledge()
        
        # Learning from experience
        self.successful_strategies = []
        self.failed_strategies = []
        self.strategy_performance_history = {}
        
        # Configuration
        self.default_reasoning_types = [
            ReasoningType.CAUSAL,
            ReasoningType.ANALOGICAL,
            ReasoningType.SYSTEMS_THINKING,
            ReasoningType.ABDUCTIVE
        ]
        
        logger.info("Strategy Reasoning Manager initialized")
    
    async def generate_strategies(
        self,
        description: str,
        context: Dict[str, Any],
        assessment: Dict[str, Any],
        max_strategies: int = 5
    ) -> List[BuildStrategy]:
        """
        Generate multiple build strategies using different reasoning approaches
        """
        logger.info(f"Generating strategies for: '{description[:50]}...'")
        
        strategies = []
        
        # Generate strategies using different reasoning types
        for i, reasoning_type in enumerate(self.default_reasoning_types[:max_strategies]):
            try:
                strategy = await self._generate_single_strategy(
                    description, context, assessment, reasoning_type, i
                )
                if strategy:
                    strategies.append(strategy)
            except Exception as e:
                logger.warning(f"Failed to generate strategy with {reasoning_type}: {e}")
        
        # Generate additional strategies if we haven't reached the limit
        while len(strategies) < max_strategies:
            try:
                # Use hybrid or experimental approaches
                strategy_type = random.choice([StrategyType.HYBRID, StrategyType.EXPERIMENTAL])
                reasoning_type = random.choice(list(ReasoningType))
                
                strategy = await self._generate_creative_strategy(
                    description, context, assessment, strategy_type, reasoning_type, len(strategies)
                )
                
                if strategy and not self._is_duplicate_strategy(strategy, strategies):
                    strategies.append(strategy)
                    
            except Exception as e:
                logger.warning(f"Failed to generate creative strategy: {e}")
                break
        
        # Optimize strategy sequence
        optimized_strategies = await self._optimize_strategy_sequence(strategies)
        
        logger.info(f"Generated {len(optimized_strategies)} strategies")
        return optimized_strategies
    
    async def generate_reasoning_chain(
        self,
        problem: str,
        context: Dict[str, Any],
        reasoning_types: Optional[List[ReasoningType]] = None
    ) -> ReasoningChain:
        """
        Generate comprehensive reasoning chain for problem analysis
        """
        if not reasoning_types:
            reasoning_types = self.default_reasoning_types
        
        steps = []
        
        # Generate reasoning steps for each type
        for reasoning_type in reasoning_types:
            step = await self._create_reasoning_step(problem, context, reasoning_type)
            if step:
                steps.append(step)
        
        # Calculate overall confidence
        if steps:
            overall_confidence = sum(step.confidence for step in steps) / len(steps)
        else:
            overall_confidence = 0.0
        
        # Generate meta-reasoning
        meta_reasoning = self._generate_meta_reasoning(steps, problem)
        
        return ReasoningChain(
            goal=problem,
            steps=steps,
            overall_confidence=overall_confidence,
            meta_reasoning=meta_reasoning
        )
    
    async def evaluate_strategy_feasibility(
        self,
        strategy: BuildStrategy,
        constraints: Optional[Dict[str, Any]] = None
    ) -> FeasibilityAssessment:
        """
        Detailed feasibility analysis including risk assessment
        """
        logger.info(f"Evaluating feasibility for strategy: {strategy.name}")
        
        # Assess different feasibility dimensions
        technical_feasibility = self._assess_technical_feasibility(strategy)
        resource_feasibility = self._assess_resource_feasibility(strategy, constraints)
        time_feasibility = self._assess_time_feasibility(strategy, constraints)
        knowledge_feasibility = self._assess_knowledge_feasibility(strategy)
        
        # Determine overall feasibility level
        avg_feasibility = (technical_feasibility + resource_feasibility + 
                          time_feasibility + knowledge_feasibility) / 4.0
        
        overall_level = self._map_to_feasibility_level(avg_feasibility)
        
        # Identify critical factors
        critical_assumptions = self._identify_critical_assumptions(strategy)
        blocking_factors = self._identify_blocking_factors(strategy, avg_feasibility)
        success_enablers = self._identify_success_enablers(strategy)
        
        return FeasibilityAssessment(
            overall_feasibility=overall_level,
            technical_feasibility=technical_feasibility,
            resource_feasibility=resource_feasibility,
            time_feasibility=time_feasibility,
            knowledge_feasibility=knowledge_feasibility,
            critical_assumptions=critical_assumptions,
            blocking_factors=blocking_factors,
            success_enablers=success_enablers,
            confidence_factors={
                "technical": technical_feasibility,
                "resource": resource_feasibility,
                "time": time_feasibility,
                "knowledge": knowledge_feasibility
            }
        )
    
    async def optimize_strategy_sequence(
        self,
        strategies: List[BuildStrategy],
        criteria: Optional[OptimizationCriteria] = None
    ) -> List[BuildStrategy]:
        """
        Optimize strategy execution order for maximum success probability
        """
        if not strategies:
            return strategies
        
        if not criteria:
            criteria = OptimizationCriteria()
        
        logger.info(f"Optimizing sequence for {len(strategies)} strategies")
        
        # Score strategies based on criteria
        scored_strategies = []
        for strategy in strategies:
            score = self._calculate_strategy_score(strategy, criteria)
            scored_strategies.append((strategy, score))
        
        # Sort by score (higher is better)
        scored_strategies.sort(key=lambda x: x[1], reverse=True)
        
        # Apply dependency constraints
        optimized_order = self._resolve_dependencies([s[0] for s in scored_strategies])
        
        # Update execution order
        for i, strategy in enumerate(optimized_order):
            strategy.execution_order = i
        
        return optimized_order
    
    async def adapt_strategy_from_feedback(
        self,
        strategy: BuildStrategy,
        feedback: Dict[str, Any]
    ) -> BuildStrategy:
        """
        Dynamically adapt strategies based on execution feedback
        """
        logger.info(f"Adapting strategy '{strategy.name}' based on feedback")
        
        # Create adapted strategy (copy original)
        adapted_strategy = BuildStrategy(
            id=f"{strategy.id}_adapted_{int(time.time())}",
            name=f"{strategy.name} (Adapted)",
            description=strategy.description,
            strategy_type=strategy.strategy_type,
            reasoning_chain=strategy.reasoning_chain,
            feasibility_level=strategy.feasibility_level,
            resource_requirements=strategy.resource_requirements.copy(),
            expected_difficulty=strategy.expected_difficulty,
            success_probability=strategy.success_probability,
            approach_steps=strategy.approach_steps.copy(),
            tools_required=strategy.tools_required.copy(),
            skills_required=strategy.skills_required.copy(),
            risk_factors=strategy.risk_factors.copy(),
            mitigation_strategies=strategy.mitigation_strategies.copy(),
            failure_modes=strategy.failure_modes.copy()
        )
        
        # Apply adaptations based on feedback
        success = feedback.get('success', False)
        error_type = feedback.get('error_type', '')
        intermediate_results = feedback.get('intermediate_results', [])
        
        if not success:
            # Adapt based on failure type
            if 'resource' in error_type.lower():
                self._adapt_for_resource_constraints(adapted_strategy, feedback)
            elif 'complexity' in error_type.lower():
                self._adapt_for_complexity_issues(adapted_strategy, feedback)
            elif 'knowledge' in error_type.lower():
                self._adapt_for_knowledge_gaps(adapted_strategy, feedback)
            else:
                self._adapt_for_general_failure(adapted_strategy, feedback)
        else:
            # Adapt successful strategy for better performance
            self._optimize_successful_strategy(adapted_strategy, feedback)
        
        # Recalculate success probability
        adapted_strategy.success_probability = self._recalculate_success_probability(
            adapted_strategy, feedback
        )
        
        return adapted_strategy
    
    async def generate_alternatives(
        self,
        original_strategy: Dict[str, Any],
        failure_reason: str,
        creative_insights: List[Any],
        research_context: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate alternative strategies based on failure analysis
        """
        logger.info("Generating alternative strategies from failure analysis")
        
        alternatives = []
        
        # Generate alternatives based on failure reason
        failure_type = self._classify_failure_type(failure_reason)
        
        # Use different reasoning approaches for alternatives
        alternative_reasoning_types = self._select_alternative_reasoning_types(failure_type)
        
        for i, reasoning_type in enumerate(alternative_reasoning_types):
            try:
                alternative = await self._generate_failure_informed_strategy(
                    original_strategy,
                    failure_reason,
                    reasoning_type,
                    creative_insights,
                    research_context
                )
                
                if alternative:
                    alternatives.append(alternative)
                    
            except Exception as e:
                logger.warning(f"Failed to generate alternative with {reasoning_type}: {e}")
        
        # Generate cross-domain alternatives using creative insights
        for insight in creative_insights[:2]:  # Use top 2 insights
            try:
                cross_domain_alternative = await self._generate_cross_domain_alternative(
                    original_strategy,
                    insight,
                    failure_reason
                )
                
                if cross_domain_alternative:
                    alternatives.append(cross_domain_alternative)
                    
            except Exception as e:
                logger.warning(f"Failed to generate cross-domain alternative: {e}")
        
        return alternatives
    
    async def refine_with_research(
        self,
        strategies: List[Dict[str, Any]],
        research: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Refine strategies based on research results
        """
        logger.info("Refining strategies with research insights")
        
        refined_strategies = []
        
        for strategy in strategies:
            try:
                refined_strategy = await self._refine_strategy_with_research(strategy, research)
                refined_strategies.append(refined_strategy)
            except Exception as e:
                logger.warning(f"Failed to refine strategy '{strategy.get('name', 'unknown')}': {e}")
                refined_strategies.append(strategy)  # Keep original if refinement fails
        
        return refined_strategies
    
    # Private helper methods
    
    async def _generate_single_strategy(
        self,
        description: str,
        context: Dict[str, Any],
        assessment: Dict[str, Any],
        reasoning_type: ReasoningType,
        index: int
    ) -> Optional[BuildStrategy]:
        """Generate a single strategy using specified reasoning type"""
        
        # Generate reasoning chain
        reasoning_chain = await self._create_reasoning_chain_for_strategy(
            description, context, reasoning_type
        )
        
        # Select strategy type based on reasoning and context
        strategy_type = self._select_strategy_type(reasoning_type, context, description)
        
        # Generate strategy name and description
        strategy_name = self._generate_strategy_name(strategy_type, reasoning_type, index)
        strategy_description = self._generate_strategy_description(
            strategy_type, reasoning_type, description
        )
        
        # Generate approach steps
        approach_steps = self._generate_approach_steps(strategy_type, reasoning_chain, description)
        
        # Assess requirements and risks
        resource_requirements = self._estimate_resource_requirements(strategy_type, approach_steps)
        risk_factors = self._identify_risk_factors(strategy_type, approach_steps)
        mitigation_strategies = self._generate_mitigation_strategies(risk_factors)
        
        # Calculate success probability
        success_probability = self._calculate_initial_success_probability(
            strategy_type, reasoning_chain.overall_confidence, assessment
        )
        
        # Determine feasibility level
        feasibility_level = self._determine_feasibility_level(
            strategy_type, resource_requirements, success_probability
        )
        
        return BuildStrategy(
            id=f"strategy_{reasoning_type.value}_{index}_{int(time.time())}",
            name=strategy_name,
            description=strategy_description,
            strategy_type=strategy_type,
            reasoning_chain=reasoning_chain,
            feasibility_level=feasibility_level,
            resource_requirements=resource_requirements,
            expected_difficulty=1.0 - success_probability,
            success_probability=success_probability,
            approach_steps=approach_steps,
            tools_required=self._identify_required_tools(strategy_type, approach_steps),
            skills_required=self._identify_required_skills(strategy_type, approach_steps),
            risk_factors=risk_factors,
            mitigation_strategies=mitigation_strategies,
            failure_modes=self._identify_failure_modes(strategy_type, risk_factors),
            parallel_compatible=self._assess_parallel_compatibility(strategy_type, approach_steps)
        )
    
    async def _create_reasoning_step(
        self,
        problem: str,
        context: Dict[str, Any],
        reasoning_type: ReasoningType
    ) -> Optional[ReasoningStep]:
        """Create a single reasoning step of specified type"""
        
        reasoning_patterns = self.reasoning_patterns.get(reasoning_type, {})
        
        if reasoning_type == ReasoningType.CAUSAL:
            premise = f"To solve '{problem}', we need to understand the causal relationships involved"
            inference = "Identifying root causes will help us design targeted interventions"
            conclusion = "Focus on manipulating key causal factors for maximum effect"
            
        elif reasoning_type == ReasoningType.ANALOGICAL:
            premise = f"'{problem}' may be similar to solved problems in other domains"
            inference = "Successful patterns from analogous situations can be adapted"
            conclusion = "Apply proven solutions from similar contexts with appropriate modifications"
            
        elif reasoning_type == ReasoningType.SYSTEMS_THINKING:
            premise = f"'{problem}' exists within a complex system of interactions"
            inference = "Understanding system dynamics reveals leverage points"
            conclusion = "Target system-level changes rather than isolated components"
            
        elif reasoning_type == ReasoningType.ABDUCTIVE:
            premise = f"Given the constraints of '{problem}', what explains the best approach?"
            inference = "The most likely explanation suggests the most promising path"
            conclusion = "Pursue the explanation that best fits all available evidence"
            
        else:
            # Generic reasoning step
            premise = f"Applying {reasoning_type.value} reasoning to '{problem}'"
            inference = f"This reasoning type suggests specific solution approaches"
            conclusion = f"Use {reasoning_type.value} principles to guide strategy development"
        
        # Calculate confidence based on reasoning type and context
        confidence = self._calculate_reasoning_confidence(reasoning_type, context)
        
        # Generate supporting evidence
        evidence = self._generate_supporting_evidence(reasoning_type, problem, context)
        
        # Identify assumptions
        assumptions = self._identify_reasoning_assumptions(reasoning_type, problem)
        
        return ReasoningStep(
            reasoning_type=reasoning_type,
            premise=premise,
            inference=inference,
            conclusion=conclusion,
            confidence=confidence,
            supporting_evidence=evidence,
            assumptions=assumptions
        )
    
    def _initialize_strategy_templates(self) -> Dict[StrategyType, Dict[str, Any]]:
        """Initialize templates for different strategy types"""
        return {
            StrategyType.PHYSICS_BASED: {
                "focus": "Physical laws and principles",
                "tools": ["simulation", "mathematical_modeling", "experimental_validation"],
                "skills": ["physics_knowledge", "mathematical_analysis", "experimental_design"],
                "typical_steps": [
                    "Model physical constraints",
                    "Derive governing equations",
                    "Simulate system behavior",
                    "Validate with experiments"
                ]
            },
            StrategyType.COMPUTATIONAL: {
                "focus": "Algorithmic and computational approaches",
                "tools": ["programming", "optimization", "machine_learning", "data_analysis"],
                "skills": ["programming", "algorithm_design", "computational_thinking"],
                "typical_steps": [
                    "Define computational model",
                    "Design algorithms",
                    "Implement solution",
                    "Optimize performance"
                ]
            },
            StrategyType.HEURISTIC: {
                "focus": "Rule-based and experience-driven methods",
                "tools": ["pattern_recognition", "rule_systems", "expert_knowledge"],
                "skills": ["pattern_recognition", "heuristic_reasoning", "domain_expertise"],
                "typical_steps": [
                    "Identify relevant patterns",
                    "Apply heuristic rules",
                    "Iteratively refine approach",
                    "Validate with experience"
                ]
            },
            StrategyType.BIO_INSPIRED: {
                "focus": "Biological and evolutionary principles",
                "tools": ["evolutionary_algorithms", "swarm_intelligence", "neural_networks"],
                "skills": ["biology_knowledge", "evolutionary_thinking", "systems_biology"],
                "typical_steps": [
                    "Identify biological analogs",
                    "Extract core principles",
                    "Adapt to problem context",
                    "Evolve solution iteratively"
                ]
            },
            StrategyType.META_ALGORITHMIC: {
                "focus": "Algorithms that generate algorithms",
                "tools": ["genetic_programming", "automated_machine_learning", "meta_learning"],
                "skills": ["meta_reasoning", "algorithm_design", "automated_systems"],
                "typical_steps": [
                    "Define solution space",
                    "Design meta-algorithm",
                    "Generate candidate solutions",
                    "Evolve optimal approach"
                ]
            },
            StrategyType.HYBRID: {
                "focus": "Combination of multiple approaches",
                "tools": ["multi_modal_analysis", "ensemble_methods", "integrated_systems"],
                "skills": ["systems_integration", "multi_domain_expertise", "synthesis"],
                "typical_steps": [
                    "Identify complementary approaches",
                    "Design integration framework",
                    "Coordinate multiple methods",
                    "Synthesize results"
                ]
            }
        }
    
    def _initialize_reasoning_patterns(self) -> Dict[ReasoningType, Dict[str, Any]]:
        """Initialize patterns for different reasoning types"""
        return {
            ReasoningType.CAUSAL: {
                "focus": "cause_and_effect",
                "keywords": ["because", "causes", "leads_to", "results_in"],
                "confidence_factors": ["data_quality", "causal_evidence", "confounding_control"]
            },
            ReasoningType.ANALOGICAL: {
                "focus": "similarity_mapping",
                "keywords": ["like", "similar_to", "analogous", "parallel"],
                "confidence_factors": ["similarity_degree", "domain_knowledge", "adaptation_success"]
            },
            ReasoningType.SYSTEMS_THINKING: {
                "focus": "holistic_analysis",
                "keywords": ["system", "interactions", "emergent", "feedback"],
                "confidence_factors": ["system_understanding", "complexity_handling", "leverage_identification"]
            },
            ReasoningType.ABDUCTIVE: {
                "focus": "best_explanation",
                "keywords": ["explains", "accounts_for", "best_fits", "most_likely"],
                "confidence_factors": ["explanation_quality", "evidence_support", "alternative_elimination"]
            }
        }
    
    def _initialize_domain_knowledge(self) -> Dict[str, List[str]]:
        """Initialize domain-specific knowledge base"""
        return {
            "physics": ["mechanics", "thermodynamics", "electromagnetism", "quantum_mechanics"],
            "mathematics": ["calculus", "linear_algebra", "statistics", "optimization"],
            "computer_science": ["algorithms", "data_structures", "machine_learning", "distributed_systems"],
            "engineering": ["control_theory", "signal_processing", "materials_science", "systems_engineering"],
            "biology": ["genetics", "evolution", "ecology", "molecular_biology"],
            "chemistry": ["organic_chemistry", "physical_chemistry", "materials_chemistry"],
            "psychology": ["cognitive_science", "behavioral_psychology", "social_psychology"]
        }
    
    # Additional helper methods would continue here...
    # Due to length constraints, I'll include key methods for strategy generation and evaluation
    
    def _select_strategy_type(self, reasoning_type: ReasoningType, context: Dict[str, Any], description: str) -> StrategyType:
        """Select appropriate strategy type based on reasoning and context"""
        
        # Analyze problem description for domain indicators
        description_lower = description.lower()
        
        if any(word in description_lower for word in ['physics', 'mechanical', 'force', 'energy']):
            return StrategyType.PHYSICS_BASED
        elif any(word in description_lower for word in ['algorithm', 'compute', 'software', 'data']):
            return StrategyType.COMPUTATIONAL
        elif any(word in description_lower for word in ['biology', 'organic', 'evolution', 'natural']):
            return StrategyType.BIO_INSPIRED
        elif reasoning_type == ReasoningType.SYSTEMS_THINKING:
            return StrategyType.HYBRID
        elif reasoning_type == ReasoningType.ANALOGICAL:
            return StrategyType.HEURISTIC
        else:
            return StrategyType.EXPERIMENTAL
    
    def _calculate_strategy_score(self, strategy: BuildStrategy, criteria: OptimizationCriteria) -> float:
        """Calculate overall strategy score based on optimization criteria"""
        
        score = 0.0
        
        # Base score from success probability
        score += strategy.success_probability * 0.4
        
        # Adjust for criteria preferences
        if criteria.prioritize_speed:
            # Lower complexity strategies score higher
            speed_factor = 1.0 - (strategy.expected_difficulty * 0.5)
            score += speed_factor * 0.3
        
        if criteria.prioritize_reliability:
            # Higher feasibility scores better
            reliability_factor = 1.0 if strategy.feasibility_level == FeasibilityLevel.STRAIGHTFORWARD else 0.5
            score += reliability_factor * 0.3
        
        if criteria.prioritize_novelty:
            # Experimental and hybrid strategies score higher
            novelty_factor = 1.0 if strategy.strategy_type in [StrategyType.EXPERIMENTAL, StrategyType.HYBRID] else 0.7
            score += novelty_factor * 0.2
        
        if criteria.prioritize_learning:
            # Strategies with high reasoning confidence score higher
            learning_factor = strategy.reasoning_chain.overall_confidence
            score += learning_factor * 0.2
        
        return score
    
    async def _optimize_strategy_sequence(self, strategies: List[BuildStrategy]) -> List[BuildStrategy]:
        """Internal method to optimize strategy sequence"""
        if len(strategies) <= 1:
            return strategies
        
        # Simple optimization: sort by success probability, then by feasibility
        def strategy_key(strategy):
            feasibility_scores = {
                FeasibilityLevel.STRAIGHTFORWARD: 5,
                FeasibilityLevel.MODERATE: 4,
                FeasibilityLevel.CHALLENGING: 3,
                FeasibilityLevel.EXTREMELY_DIFFICULT: 2,
                FeasibilityLevel.IMPOSSIBLE: 1
            }
            
            return (
                strategy.success_probability,
                feasibility_scores.get(strategy.feasibility_level, 0),
                -strategy.expected_difficulty
            )
        
        return sorted(strategies, key=strategy_key, reverse=True)