"""
Test Enhanced Snake Agent with VLTM Integration

This test validates that the enhanced Snake Agent properly integrates with the
Very Long-Term Memory system and can store and retrieve memories.
"""

import asyncio
import json
import logging
import os
import tempfile
import uuid
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Configure logging for testing
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def mock_agi_system():
    """Create a mock AGI system for testing"""
    mock_system = Mock()
    mock_system.config = Mock()
    return mock_system

async def test_enhanced_snake_agent_vltm_integration():
    """Test enhanced snake agent VLTM integration"""
    
    print("=" * 70)
    print("Enhanced Snake Agent VLTM Integration Test")
    print("=" * 70)
    
    # Set environment variables for testing
    os.environ['SNAKE_VLTM_ENABLED'] = 'true'
    os.environ['SNAKE_MAX_THREADS'] = '2'
    os.environ['SNAKE_MAX_PROCESSES'] = '2'
    
    try:
        # Create temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            os.environ['SNAKE_VLTM_STORAGE_DIR'] = temp_dir
            
            # Import after setting environment variables
            from core.snake_agent_enhanced import EnhancedSnakeAgent
            
            print("1. Initializing Enhanced Snake Agent with VLTM...")
            
            # Create mock AGI system
            mock_agi = mock_agi_system()
            
            # Create enhanced snake agent
            agent = EnhancedSnakeAgent(mock_agi)
            
            print(f"   ✓ Agent created with session ID: {agent.session_id}")
            print(f"   ✓ VLTM enabled: {agent.vltm_enabled}")
            print(f"   ✓ VLTM storage directory: {agent.vltm_storage_dir}")
            
            # Test basic configuration
            print("\n2. Testing Configuration...")
            print(f"   ✓ Max threads: {agent.snake_config.max_threads}")
            print(f"   ✓ Max processes: {agent.snake_config.max_processes}")
            print(f"   ✓ Session ID: {agent.session_id}")
            
            # Test status before initialization
            print("\n3. Testing Status (Before Initialization)...")
            status = agent.get_status()
            print(f"   ✓ Running: {status['running']}")
            print(f"   ✓ Initialized: {status['initialized']}")
            print(f"   ✓ VLTM enabled: {status['components']['vltm_enabled']}")
            
            # Note: We can't fully initialize due to missing dependencies in test environment
            # Instead, we'll test the VLTM components would be properly configured
            print("\n4. Testing VLTM Component Configuration...")
            
            # Verify VLTM components are properly configured
            assert agent.vltm_enabled == True, "VLTM should be enabled"
            assert agent.session_id is not None, "Session ID should be set"
            assert agent.vltm_storage_dir is not None, "VLTM storage directory should be set"
            
            print("   ✓ VLTM is enabled")
            print("   ✓ Session ID is configured")
            print("   ✓ Storage directory is configured")
            
            # Test memory storage method existence
            print("\n5. Testing Memory Storage Methods...")
            
            assert hasattr(agent, '_store_file_change_memory'), "File change memory storage method exists"
            assert hasattr(agent, '_store_experiment_memory'), "Experiment memory storage method exists"
            assert hasattr(agent, 'get_vltm_insights'), "VLTM insights method exists"
            assert hasattr(agent, 'trigger_memory_consolidation'), "Memory consolidation trigger exists"
            
            print("   ✓ File change memory storage method available")
            print("   ✓ Experiment memory storage method available")
            print("   ✓ VLTM insights method available")
            print("   ✓ Memory consolidation trigger available")
            
            # Test state persistence with VLTM
            print("\n6. Testing State Persistence...")
            
            # Create some test metrics
            agent.improvements_applied = 5
            agent.experiments_completed = 10
            agent.files_analyzed = 25
            agent.start_time = datetime.now()
            
            # Save state
            await agent._save_state()
            
            # Verify state file exists
            assert agent.state_file.exists(), "State file should be created"
            
            # Load state and verify
            await agent._load_state()
            assert agent.improvements_applied == 5, "Improvements applied should be restored"
            assert agent.experiments_completed == 10, "Experiments completed should be restored"
            assert agent.files_analyzed == 25, "Files analyzed should be restored"
            
            print("   ✓ State saved successfully")
            print("   ✓ State loaded successfully")
            print("   ✓ Metrics preserved across save/load")
            
            # Test enhanced status with VLTM
            print("\n7. Testing Enhanced Status...")
            
            status = await agent.get_status()
            
            # Verify VLTM status is included
            assert 'vltm_status' in status, "VLTM status should be included"
            vltm_status = status['vltm_status']
            
            assert vltm_status['enabled'] == True, "VLTM should be enabled in status"
            assert 'session_id' in vltm_status, "Session ID should be in VLTM status"
            assert 'storage_dir' in vltm_status, "Storage directory should be in VLTM status"
            
            print("   ✓ VLTM status included in agent status")
            print(f"   ✓ VLTM session ID: {vltm_status['session_id']}")
            print(f"   ✓ VLTM storage directory: {vltm_status['storage_dir']}")
            
            # Test cleanup
            print("\n8. Testing Cleanup...")
            await agent._cleanup()
            print("   ✓ Cleanup completed successfully")
            
            print("\n" + "=" * 70)
            print("✅ ENHANCED SNAKE AGENT VLTM INTEGRATION TEST PASSED")
            print("=" * 70)
            print("\nIntegration Status:")
            print("✅ VLTM Configuration - Complete")
            print("✅ Memory Storage Methods - Complete")
            print("✅ State Persistence Enhancement - Complete")
            print("✅ Status Reporting Enhancement - Complete")
            print("✅ Cleanup Integration - Complete")
            print("\n🎯 Enhanced Snake Agent is ready for VLTM operation!")
            
            return True
            
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        logger.error(f"Integration test failed: {e}", exc_info=True)
        return False
    
    finally:
        # Clean up environment variables
        for var in ['SNAKE_VLTM_ENABLED', 'SNAKE_VLTM_STORAGE_DIR', 'SNAKE_MAX_THREADS', 'SNAKE_MAX_PROCESSES']:
            if var in os.environ:
                del os.environ[var]


async def test_memory_storage_simulation():
    """Test memory storage simulation without full initialization"""
    
    print("\n" + "=" * 70)
    print("Memory Storage Simulation Test")
    print("=" * 70)
    
    try:
        from core.snake_agent_enhanced import EnhancedSnakeAgent
        from core.snake_data_models import FileChangeEvent
        
        # Create mock agent components
        mock_agi = mock_agi_system()
        agent = EnhancedSnakeAgent(mock_agi)
        
        # Mock VLTM store
        mock_vltm_store = AsyncMock()
        mock_vltm_store.store_memory.return_value = "test_memory_id_123"
        agent.vltm_store = mock_vltm_store
        
        print("1. Testing File Change Memory Storage...")
        
        # Create test file change event
        file_event = FileChangeEvent(
            file_path="test_file.py",
            event_type="modified",
            file_hash="abc123",
            old_hash="def456"
        )
        
        # Test file change memory storage
        await agent._store_file_change_memory(file_event)
        
        # Verify store_memory was called
        mock_vltm_store.store_memory.assert_called()
        call_args = mock_vltm_store.store_memory.call_args
        
        assert call_args is not None, "store_memory should have been called"
        
        # Check the content structure
        content = call_args[1]['content']  # kwargs['content']
        assert content['event_type'] == 'file_change', "Event type should be file_change"
        assert content['file_path'] == 'test_file.py', "File path should be preserved"
        assert content['session_id'] == agent.session_id, "Session ID should be included"
        
        print("   ✓ File change memory stored successfully")
        print(f"   ✓ Content includes session ID: {content['session_id']}")
        
        print("\n2. Testing Experiment Memory Storage...")
        
        # Reset mock
        mock_vltm_store.reset_mock()
        
        # Create test experiment result
        experiment_result = {
            "task_id": "exp_test_123",
            "success": True,
            "type": "optimization",
            "data": {"file": "test.py"},
            "results": {"improvement": 0.15}
        }
        
        # Test experiment memory storage
        await agent._store_experiment_memory(experiment_result)
        
        # Verify store_memory was called again
        mock_vltm_store.store_memory.assert_called()
        call_args = mock_vltm_store.store_memory.call_args
        
        # Check the content structure
        content = call_args[1]['content']
        assert content['event_type'] == 'experiment_result', "Event type should be experiment_result"
        assert content['success'] == True, "Success should be preserved"
        assert content['session_id'] == agent.session_id, "Session ID should be included"
        
        print("   ✓ Experiment memory stored successfully")
        print(f"   ✓ Experiment ID: {content['experiment_id']}")
        print(f"   ✓ Success status: {content['success']}")
        
        print("\n3. Testing VLTM Insights...")
        
        # Mock search results
        mock_vltm_store.search_memories.return_value = [
            {"memory_id": "insight_1", "content": {"insight": "Test insight 1"}},
            {"memory_id": "insight_2", "content": {"insight": "Test insight 2"}}
        ]
        
        insights = await agent.get_vltm_insights("optimization patterns")
        
        assert len(insights) == 2, "Should return 2 insights"
        assert insights[0]['memory_id'] == 'insight_1', "First insight should match"
        
        print("   ✓ VLTM insights retrieved successfully")
        print(f"   ✓ Retrieved {len(insights)} insights")
        
        print("\n✅ MEMORY STORAGE SIMULATION TEST PASSED")
        return True
        
    except Exception as e:
        print(f"\n❌ SIMULATION TEST FAILED: {e}")
        logger.error(f"Simulation test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    """Run the integration tests"""
    
    async def run_all_tests():
        print("Starting Enhanced Snake Agent VLTM Integration Tests...\n")
        
        # Run main integration test
        success1 = await test_enhanced_snake_agent_vltm_integration()
        
        # Run memory storage simulation test
        success2 = await test_memory_storage_simulation()
        
        if success1 and success2:
            print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            print("\nNext Steps:")
            print("1. ✅ Snake Agent VLTM integration complete")
            print("2. 🔄 Proceed to data migration utilities")
            print("3. 🔄 Implement advanced retrieval system")
            print("4. 🔄 Complete Phase 3 integration tasks")
        else:
            print("\n❌ Some tests failed. Please review and fix issues.")
    
    # Run the tests
    asyncio.run(run_all_tests())