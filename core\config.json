{"zuki": {"api_key": "985160dfa1fd499fd12af708d16552e37a8c6f77cbfb50ae400e3ff33fbd791bc7b3b82625379a1f5ca7568f1ee04eb81a0f8c06f0ba6c276d3dddfe13e9c18d", "base_url": "https://api.zukijourney.com/v1", "models": ["gpt-4o:online", "gpt-4o", "deepseek-chat", "deepseek-reasoner"]}, "electronhub": {"api_key": "ek-sVvxMYfdFQ0Kl6Aj2tmV7b8n5v0Y0sDHVsOUZWyx2vbs0AbuAc", "base_url": "https://api.electronhub.ai", "models": ["deepseek-v3-0324", "gpt-4o-2024-11-20"]}, "zanity": {"api_key": "vc-b1EbB_BekM2TCPol64yDe7FgmOM34d4q", "base_url": "https://api.zanity.xyz/v1", "models": ["deepseek-r1", "deepseek-v3-0324", "gpt-4o:free", "claude-3.5-sonnet:free", "qwen-max-0428"]}, "provider1": {"models": [{"name": "deepseek-ai/DeepSeek-V3-0324", "vision": false}, {"name": "deepseek-ai/DeepSeek-R1-0528", "vision": false}, {"name": "command-r", "vision": false}]}, "provider2": {"models": [{"name": "gpt-4o", "vision": true}, {"name": "llama-4-scout", "vision": false}]}, "provider3": {"models": [{"name": "gpt-4o", "vision": true}, {"name": "claude-3.7-sonnet", "vision": false}, {"name": "llama-4-scout", "vision": false}, {"name": "deepseek-v3-0324", "vision": true}]}, "a4f": {"api_key": "ddc-a4f-7bbefd7518a74b36b1d32cb867b1931f", "base_url": "https://api.a4f.co/v1"}, "local_situation_generator": {"enabled": true, "schedule": "daily", "default_interest_areas": ["technology", "finance", "health"], "use_trends": true}, "main_llm_decision_maker": {"enabled": true, "preferred_model": "gpt-4o", "chain_of_thought": true, "rag_enabled": true}, "gemini": {"api_keys": [{"id": "gemini_key_1", "key": "AIzaSyBW-aVU-x7JCjBJVVKjPGUacups0-GBHvQ", "priority": 1, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_2", "key": "AIzaSyBW-aVU-x7JCjBJVVKjPGUacups0-GBHvQ", "priority": 2, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_3", "key": "AIzaSyC8hTaXE6jkWDFZyZDybFEoHfPR-4VSV-k", "priority": 3, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_4", "key": "AIzaSyC5-FvfvCTkD4VdQ96lmi0Yv1r6Z9enuzo", "priority": 4, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_5", "key": "AIzaSyBzKrEG8xBn9adqfOMhfNZcXNiwTIytEZM", "priority": 5, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_6", "key": "AIzaSyBNeVd7a-haGinjRpluJ-zlG3jP2RUTEeE", "priority": 6, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_7", "key": "AIzaSyDw0Xb4_McwTdmyU9ag12AesFpGTNx_txU", "priority": 7, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_8", "key": "AIzaSyD5aoPRLOo8mlsVq1SFQtfIk1LvYhkAfVI", "priority": 8, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_9", "key": "AIzaSyAsg16VugG9mnGfu2CZxJ4kL1tFrNE-Pl0", "priority": 9, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}, {"id": "gemini_key_10", "key": "AIzaSyBCQic67-TJmSXUVTCiW3SFU5fLZx7enII", "priority": 10, "rate_limit_reset_time": null, "failure_count": 0, "last_success": null}], "rate_limit": {"requests_per_minute": 60, "cooldown_period": 300, "max_retries": 3, "backoff_factor": 2.0}, "fallback": {"enabled": true, "max_key_failures": 5}}}