# MOBIUS AGI Development Guide

This guide provides information for developers who want to contribute to or extend the MOBIUS AGI system.

## Getting Started

### Prerequisites

- Python 3.8+
- uv (for dependency management)
- Git
- IDE or text editor of your choice

### Development Environment Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/OpenSource-Syndicate/RAVANA.git
   cd RAVANA
   ```

2. Create a virtual environment:
   ```bash
   uv venv
   ```

3. Activate the virtual environment:
   ```bash
   # Windows
   .venv\Scripts\activate
   
   # macOS/Linux
   source .venv/bin/activate
   ```

4. Install dependencies:
   ```bash
   uv pip install -e .
   ```

### Project Structure

```
RAVANA/
├── core/              # Core system components
├── modules/           # Specialized functionality modules
├── services/          # Shared services
├── database/          # Database components
├── tests/             # Test suite
├── docs/              # Documentation
├── main.py            # Entry point
└── README.md          # Project overview
```

## Contributing Guidelines

### Code of Conduct

All contributors are expected to follow our Code of Conduct:
- Be respectful and inclusive
- Provide constructive feedback
- Focus on technical merit
- Maintain professional standards

### How to Contribute

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests for your changes
5. Update documentation as needed
6. Submit a pull request

### Pull Request Process

1. Ensure any install or build dependencies are removed
2. Update the README.md with details of changes to the interface
3. Increase the version numbers in any examples files and the README.md
4. Your pull request will be reviewed by maintainers
5. Address any feedback during the review process

## Development Workflow

### Branching Strategy

We use a feature branching model:
- `main` branch contains production-ready code
- Feature branches for new functionality
- Hotfix branches for urgent bug fixes
- Release branches for version preparation

### Commit Guidelines

Follow conventional commit messages:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `refactor:` for code refactoring
- `test:` for adding or updating tests
- `chore:` for maintenance tasks

Example:
```
feat: add curiosity-driven goal generation
- Implement novelty detection algorithm
- Add goal prioritization based on curiosity
- Update self-reflection module to incorporate curiosity metrics
```

### Code Review Process

All changes must be reviewed before merging:
- At least one maintainer approval required
- Automated tests must pass
- Code style guidelines must be followed
- Documentation must be updated

## Coding Standards

### Python Style Guide

We follow PEP 8 with some additional conventions:
- Use 4 spaces for indentation
- Maximum line length of 88 characters
- Use descriptive variable and function names
- Include docstrings for all public functions and classes
- Use type hints where possible

### Documentation Standards

- All public APIs must be documented
- Use Google-style docstrings
- Include examples for complex functionality
- Keep documentation up to date with code changes

### Testing Requirements

- Write unit tests for new functionality
- Maintain test coverage above 80%
- Include integration tests for complex features
- Use pytest for test framework

## Module Development

### Creating a New Module

1. Create a new directory in `modules/`
2. Implement the module interface
3. Add configuration files
4. Write tests
5. Document the module

### Module Interface

All modules should implement the standard interface:

```python
class BaseModule:
    def __init__(self, config):
        self.config = config
    
    def initialize(self):
        """Initialize the module"""
        pass
    
    def execute(self, context):
        """Execute module functionality"""
        pass
    
    def shutdown(self):
        """Clean up resources"""
        pass
```

### Configuration

Modules should support configuration through:
- JSON configuration files
- Environment variables
- Command-line arguments
- Default values

## Service Development

### Creating a New Service

1. Create a new file in `services/`
2. Implement the service interface
3. Register the service
4. Write tests
5. Document the service

### Service Interface

All services should implement the standard interface:

```python
class BaseService:
    def __init__(self, config):
        self.config = config
        self.initialized = False
    
    def initialize(self):
        """Initialize the service"""
        self.initialized = True
    
    def shutdown(self):
        """Clean up resources"""
        self.initialized = False
```

## Action Development

### Creating a New Action

1. Create a new file in `core/actions/`
2. Inherit from the base Action class
3. Implement the execute method
4. Register the action
5. Write tests
6. Document the action

### Action Interface

All actions should implement the standard interface:

```python
from core.actions.action import BaseAction

class CustomAction(BaseAction):
    def __init__(self, name, config):
        super().__init__(name, config)
    
    def execute(self, context):
        """Execute the action"""
        # Implementation here
        pass
    
    def validate(self, context):
        """Validate preconditions"""
        # Validation logic here
        pass
```

## Testing

### Test Structure

Tests are organized as follows:
- Unit tests in `tests/`
- Integration tests in `tests/integration/`
- Performance tests in `tests/performance/`

### Running Tests

Run all tests:
```bash
pytest
```

Run specific test files:
```bash
pytest tests/test_module.py
```

Run tests with coverage:
```bash
pytest --cov=.
```

### Writing Tests

Follow these guidelines for writing tests:
- Use descriptive test names
- Test one behavior per test
- Use fixtures for setup and teardown
- Mock external dependencies
- Include edge cases

## Debugging

### Logging

Use the built-in logging system:
- Different log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Module-specific loggers
- Structured logging for complex data

### Debugging Tools

Recommended debugging tools:
- pdb for interactive debugging
- logging for trace information
- pytest for test-driven development
- IDE debugging features

## Performance Optimization

### Profiling

Use profiling tools to identify bottlenecks:
- cProfile for CPU profiling
- memory_profiler for memory usage
- line_profiler for line-by-line analysis

### Optimization Techniques

Common optimization approaches:
- Algorithmic improvements
- Caching frequently accessed data
- Lazy evaluation
- Parallel processing where appropriate
- Memory management

## Security Considerations

### Secure Coding Practices

Follow these security guidelines:
- Validate all input data
- Sanitize output data
- Use secure communication protocols
- Implement proper authentication and authorization
- Protect sensitive information

### Vulnerability Reporting

Report security vulnerabilities through:
- Private GitHub security advisories
- <NAME_EMAIL>
- Coordinated disclosure process

## Release Process

### Versioning

We follow Semantic Versioning (SemVer):
- MAJOR version for incompatible API changes
- MINOR version for backward-compatible functionality
- PATCH version for backward-compatible bug fixes

### Release Steps

1. Update version number in pyproject.toml
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Create Git tag
6. Publish release on GitHub
7. Update documentation

## Community

### Communication Channels

- GitHub Issues for bug reports and feature requests
- GitHub Discussions for general discussion
- Slack community (link in README)
- Monthly developer meetings

### Getting Help

If you need help:
1. Check the documentation
2. Search existing issues
3. Ask in discussions
4. Join community calls